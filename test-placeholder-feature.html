<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Placeholder Input Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }

        .placeholder-input-container {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 8px;
            min-height: 50px;
            background-color: #fff;
            cursor: text;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            gap: 5px;
        }

        .placeholder-input-container:focus-within {
            border-color: #ffce32;
            box-shadow: 0 0 0 0.2rem rgba(255, 206, 50, 0.25);
        }

        .placeholder-badge {
            background-color: #ffce32;
            color: #000;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin: 2px;
        }

        .placeholder-badge .remove-badge {
            cursor: pointer;
            font-weight: bold;
            color: #666;
            margin-left: 3px;
        }

        .placeholder-badge .remove-badge:hover {
            color: #000;
        }

        .placeholder-input {
            border: none;
            outline: none;
            background: transparent;
            flex: 1;
            min-width: 120px;
            padding: 4px;
            font-size: 14px;
        }

        .placeholder-display {
            font-family: monospace;
            color: #666;
            font-size: 11px;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .form_field_padding {
            margin-bottom: 20px;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
            display: block;
        }

        .text-muted {
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Dynamic Placeholder Input Feature Test</h1>
    
    <form>
        <div class="form_field_padding">
            <label for="placeholders">Available Placeholders</label>
            <div class="placeholder-input-container" id="placeholderContainer">
                <input type="text" class="placeholder-input" id="placeholderInput" 
                       placeholder="Type placeholder name and press Enter">
            </div>
            <small class="text-muted">
                Type placeholder names and press Enter to add them. Only lowercase letters and underscores allowed.
            </small>
            <div id="placeholderDisplay" class="placeholder-display"></div>
        </div>
    </form>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const placeholderContainer = document.getElementById('placeholderContainer');
        const placeholderInput = document.getElementById('placeholderInput');
        const placeholderDisplay = document.getElementById('placeholderDisplay');
        let placeholders = [];

        // Handle input events
        placeholderInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const value = this.value.trim();
                
                if (value) {
                    // Clean the input: remove special characters, replace spaces with underscores, convert to lowercase
                    const cleanValue = value
                        .toLowerCase()
                        .replace(/[^a-z0-9\s_]/g, '') // Remove special characters except spaces and underscores
                        .replace(/\s+/g, '_') // Replace spaces with underscores
                        .replace(/_+/g, '_') // Replace multiple underscores with single
                        .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
                    
                    if (cleanValue && !placeholders.includes(cleanValue)) {
                        addPlaceholder(cleanValue);
                        this.value = '';
                        updateDisplay();
                        updateHiddenInputs();
                    }
                }
            }
        });

        // Handle container click to focus input
        placeholderContainer.addEventListener('click', function(e) {
            if (e.target === this) {
                placeholderInput.focus();
            }
        });

        function addPlaceholder(placeholder) {
            if (!placeholders.includes(placeholder)) {
                placeholders.push(placeholder);
                
                // Create badge element
                const badge = document.createElement('div');
                badge.className = 'placeholder-badge';
                badge.innerHTML = `
                    ${placeholder}
                    <span class="remove-badge" onclick="removePlaceholder('${placeholder}')">&times;</span>
                `;
                
                // Insert before the input
                placeholderContainer.insertBefore(badge, placeholderInput);
            }
        }

        window.removePlaceholder = function(placeholder) {
            const index = placeholders.indexOf(placeholder);
            if (index > -1) {
                placeholders.splice(index, 1);
                
                // Remove the badge element
                const badges = placeholderContainer.querySelectorAll('.placeholder-badge');
                badges.forEach(badge => {
                    if (badge.textContent.trim().replace('×', '').trim() === placeholder) {
                        badge.remove();
                    }
                });
                
                updateDisplay();
                updateHiddenInputs();
            }
        };

        function updateDisplay() {
            if (placeholders.length > 0) {
                const displayText = placeholders.map(p => `{{${p}}}`).join(', ');
                placeholderDisplay.innerHTML = `<strong>Template format:</strong> ${displayText}`;
            } else {
                placeholderDisplay.innerHTML = '<em>No placeholders added yet. Type a placeholder name and press Enter.</em>';
            }
        }

        function updateHiddenInputs() {
            // Remove existing hidden inputs
            const existingInputs = document.querySelectorAll('input[name="placeholder[]"]');
            existingInputs.forEach(input => input.remove());
            
            // Add new hidden inputs
            placeholders.forEach(placeholder => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'placeholder[]';
                hiddenInput.value = placeholder;
                placeholderContainer.appendChild(hiddenInput);
            });
            
            console.log('Hidden inputs updated:', placeholders);
        }

        // Initial display update
        updateDisplay();
        updateHiddenInputs();

        // Demo: Add some sample placeholders
        setTimeout(() => {
            addPlaceholder('customer_name');
            addPlaceholder('booking_date');
            updateDisplay();
            updateHiddenInputs();
        }, 1000);
    });
    </script>
</body>
</html>
